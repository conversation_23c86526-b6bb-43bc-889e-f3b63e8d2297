﻿@inject IGenericRepository<ProgramModel> ProgramRepo
<select class="form-control" @onchange="OnProgramChange">
	<option value="">-- Select program --</option>
	@foreach (var program in programList)
	{
		<option value="@program.ProgramId">@program.Name</option>
	}
</select>


@code {
	public List<ProgramModel> programList { get; set; } = new();
	[Parameter]
	public int SelectedProgramId { get; set; }

	[Parameter]
	public EventCallback<int> SelectedProgramIdChanged { get; set; }

	protected override async Task OnInitializedAsync()
	{
		programList = (await ProgramRepo.GetAllAsync("GetAllPrograms"))
					.Where(p => p.IsActive == 1)
					.ToList();

	}
	private async Task OnProgramChange(ChangeEventArgs e)
	{
		if(int.TryParse(e.Value?.ToString(),out var id))
		{
			SelectedProgramId = id;
			await SelectedProgramIdChanged.InvokeAsync(id);

		}
	}
}
