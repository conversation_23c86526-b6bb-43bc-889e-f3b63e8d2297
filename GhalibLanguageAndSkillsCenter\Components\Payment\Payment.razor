﻿@page "/payment"
@using GhalibLanguageAndSkillsCenter.Models.Payment
@inject IGenericRepository<StudentModel> StudentRepo
@inject IGenericRepository<ProgramModel> ProgramRepo
@inject IGenericRepository<DiscountModel> DiscountRepo
@inject IGenericRepository<StudentPaymentModel> PaymentRepo
@inject IGenericRepository<GeneralDiscounModel> GeneralDiscountRepo
@rendermode InteractiveServer

<div class="shadow-lg p-4">
    <h3>Payment</h3>

    <div class="row mb-3">
        <div class="col-md-6">
            <!-- Program Selection -->
            <InputSelect class="form-control"
            Value="SelectedProgramId"
            ValueChanged="@(async (int id) => await OnProgramChanged(id))"
            ValueExpression="() => SelectedProgramId">
                <option value="0">-- Select program --</option>
                @foreach (var program in programs)
                {
                    <option value="@program.ProgramId">@program.Name</option>
                }
            </InputSelect>
        </div>

        <div class="col-md-6">
            <!-- Student Selection -->
            <InputSelect class="form-control"
            @bind-Value="SelectedStudentId"
            disabled="@(students == null || students.Count == 0)">
                <option value="0">-- Select student --</option>
                @if (students != null && students.Count == 0 && SelectedProgramId > 0)
                {
                    <option disabled>-- No students available --</option>
                }
                else
                {
                    @foreach (var student in students)
                    {
                        <option value="@student.StudentId">@student.Name @student.LastName</option>
                    }
                }
            </InputSelect>
        </div>
    </div>

    @if (SelectedProgramId != 0 && SelectedStudentId != 0)
    {
        <div class="row mb-3">
            <div class="col-md-2">
                <label class="form-label">Student Discount (%)</label>
                <input class="form-control"
                value="@studentdiscount"
                disabled />
            </div>
            <div class="col-md-2">
                <label class="form-label">Program Discount (%)</label>
                <input class="form-control"
                value="@ProgramDiscount"
                disabled />
            </div>

            <div class="col-md-2">
                <label class="form-label">Total Discount (%)</label>
                <input class="form-control"
                value="@TotalDiscount"
                disabled />
            </div>
            <div class="col-md-2">
                <label class="form-label">Total Fee</label>
                <InputNumber class="form-control"
                @bind-Value="totalFee"
                Disabled="true" />
            </div>
            <div class="col-md-2">
                <label class="form-label">Final Fee</label>
                <input type="text"
                value="@FinalFee"
                class="form-control"
                disabled />
            </div>
            <div class="col-md-2">
                <label class="form-label">Total Paid</label>
                <input type="text"
                value="@paymentInfo.TotalPaid"
                class="form-control"
                disabled />
            </div>
            <div class="col-md-2">
                <label class="form-label">Remaining Amount</label>
                <input type="number"
                value="@paymentInfo.RemainingAmount"
                class="form-control"
                disabled />
            </div>
        </div>

        <!-- Payment Form -->
        <EditForm Model="paymentModel" OnValidSubmit="HandleValidSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />

            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">Pay Amount</label>
                    <InputNumber @bind-Value="paymentModel.PaidAmount"
                    class="form-control" />
                </div>
                <div class="col-md-4">
                    <label class="form-label">Payment Date</label>
                    <InputPersianDatePicker CssClass="form-control" @bind-Value="paymentDate"></InputPersianDatePicker>
                </div>
                <div class="col-md-4 align-self-end">
                    <button type="submit" class="btn btn-primary">Save Payment</button>
                </div>
            </div>
        </EditForm>

        @if (!string.IsNullOrEmpty(saveMessage))
        {
            <div class="alert alert-info">@saveMessage</div>
        }
    }
</div>
@if(SelectedStudentId !=0)
{
    <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.StudentPaymentHistory
    StudentId ="@SelectedStudentId" ProgramId="@SelectedProgramId" RefereshToken="LoadHistory"
    ></GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.StudentPaymentHistory>
}

@code {
    private int SelectedProgramId { get; set; }
    private string paymentDate { get; set; }

    private int _selectedStudentId;
    public int SelectedStudentId
    {
        get => _selectedStudentId;
        set
        {
            if (_selectedStudentId == value) return;
            _selectedStudentId = value;
            _ = OnStudentChanged(value);
        }
    }

    private List<ProgramModel> programs = new();
    private List<StudentModel> students = new();
    private DiscountModel? discount;
    private string? studentdiscount;
    private int totalFee;
    private List<StudentPaymentModel> paymentDetails = new();
    private StudentPaymentModel paymentInfo = new();
    private string? FinalFee;
    private int LoadHistory = 0;
    private string? ProgramDiscount;
    private decimal TotalDiscount;

    // New payment form model and message
    private StudentPaymentModel paymentModel = new();
    private string saveMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        programs = (await ProgramRepo.GetAllAsync("GetAllPrograms"))
           .Where(p => p.IsActive ==1)
           .ToList();

    }

    private async Task OnProgramChanged(int programId)
    {
        SelectedProgramId = programId;
        TotalDiscount = 0;
        students = programId > 0
            ? (await StudentRepo.GetAllAsyncById("GetAllStudentsByProgram", new { ProgramId = programId })).ToList()
            : new List<StudentModel>();

        _selectedStudentId = 0;
        discount = null;
        studentdiscount = null;
        totalFee = programs.FirstOrDefault(p => p.ProgramId == programId)?.Fee ?? 0;

        GeneralDiscounModel general = new();

        general = await (GeneralDiscountRepo.GetByIdAsync("GetProgramDiscount", new { programId = SelectedProgramId }));
        if(general != null)
        {

            TotalDiscount = general.DiscountPercentage;
            ProgramDiscount = general.DiscountPercentage.ToString();
        }
        else{
            ProgramDiscount = "No Active Discount";
        }
        paymentInfo = new StudentPaymentModel();
        saveMessage = string.Empty;

        StateHasChanged();
    }

    private async Task OnStudentChanged(int studentId)
    {
        SelectedStudentId = studentId;
        if (studentId > 0)
        {
            discount = await DiscountRepo.GetByIdAsync("GetStudentDiscount", new { studentid = studentId,ProgramId=SelectedProgramId });
            if(discount != null && discount.IsActive == 1)
            {
                TotalDiscount = (decimal.TryParse(ProgramDiscount, out var pd) ? pd : 0m)
                          + discount.DiscountPercentage;

                studentdiscount = discount?.DiscountPercentage.ToString();
            }
            else
            {
                FinalFee = totalFee.ToString();
                studentdiscount = "No Active Discount";
            }
                FinalFee = (totalFee - (totalFee * TotalDiscount / 100)).ToString();

            paymentDetails = (await PaymentRepo
                        .GetAllAsyncById("GetStudentPaymentHistory", new { studentId }))
                        .ToList();
            paymentInfo = paymentDetails
                .Where(p => p.ProgramId == SelectedProgramId)
                .FirstOrDefault()
              ?? new StudentPaymentModel();

            // Initialize the paymentModel defaults
            paymentModel = new StudentPaymentModel
                {
                    StudentId = studentId,
                    ProgramId = SelectedProgramId,
                    DiscountPercentage = discount?.DiscountPercentage ?? 0,
                    RemainingAmount = paymentInfo.RemainingAmount
                };
        }
        else
        {
            discount = null;
            studentdiscount = null;
            paymentInfo = new StudentPaymentModel();
        }
        saveMessage = string.Empty;
        StateHasChanged();
    }

    private async Task HandleValidSubmit()
    {
        paymentModel.PaymentDate = PersianDateConverter.ToDateTime(paymentDate);
        // Call stored procedure AddStudentPayment
        var outputParams = new Dictionary<string, object?>
        {
            { "@StudentId", SelectedStudentId },
            { "@ProgramId", SelectedProgramId },
            { "@PaidAmount", paymentModel.PaidAmount },
            { "@DiscountPercentage", paymentModel.DiscountPercentage },
            { "@PaymentDate", paymentModel.PaymentDate },
            { "@RemainingAmount", 0 },
            { "@ReturnCode", 0 }
        };

        await PaymentRepo.AddAsync("AddStudentPayment", outputParams);

        // Check return code and remaining
        var returnCode = (int)outputParams["@ReturnCode"]!;
        var remaining = (int)outputParams["@RemainingAmount"]!;
        saveMessage = returnCode == 0
            ? $"Payment saved successfully. Remaining amount: {remaining}."
            : $"Overpayment: you can only pay up to {remaining}.";
        LoadHistory++;

        // Refresh student data
        await OnStudentChanged(SelectedStudentId);


    }
    public class GeneralDiscounModel
    {
        public int GeneralDiscountId { get; set; }
        public string Title { get; set; }
        public decimal DiscountPercentage { get; set; }
        public int ProgramId { get; set; }

    }
}
