﻿@page "/country"
@using <PERSON><PERSON>
@using Microsoft.Data.SqlClient
@using System.ComponentModel.DataAnnotations
@inject IConfiguration Configuration
@rendermode InteractiveServer
@attribute [Authorize]
<h3>Country</h3>

<EditForm Model="@newCountry" OnValidSubmit="AddOrUpdateCountry" FormName="country">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newCountry.EnglishName" />
    </div>

    <div class="form-group">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newCountry.DariName" />
    </div>

    <button class="btn btn-primary mt-2" type="submit">
        @if (isEditMode)
        {
            <text>Update Country</text>
        }
        else
        {
            <text>Add Country</text>
        }
    </button>

    @if (isEditMode)
    {
        <button class="btn btn-secondary mt-2 ms-2" type="button" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

@if (isSuccess)
{
    <p class="alert alert-success mt-2">Operation completed successfully!</p>
}

@if (isError)
{
    <p class="alert alert-danger mt-2">Error: @errorMessage</p>
}

<hr />

<h3>Countries List</h3>

@if (countries is not null && countries.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Dari Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var country in countries)
            {
                <tr>
                    <td>@country.CountryId</td>
                    <td>@country.EnglishName</td>
                    <td>@country.DariName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditCountry(country)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDeleteCountry(country.CountryId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No countries available.</p>
}

<!-- Confirmation Modal -->
@if (isDeleteConfirmationVisible)
{
    <div class="modal" style="display:block;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="close" aria-label="Close" @onclick="CancelDelete">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this country?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" @onclick="DeleteCountry">Delete</button>
                    <button type="button" class="btn btn-secondary" @onclick="CancelDelete">Cancel</button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private CountryModel newCountry = new CountryModel();
    private List<CountryModel> countries = new List<CountryModel>();

    private bool isSuccess = false;
    private bool isError = false;
    private string errorMessage = "";

    private bool isEditMode = false;
    private bool isDeleteConfirmationVisible = false;
    private int countryIdToDelete;

    private string connectionString;

    protected override void OnInitialized()
    {
        connectionString = Configuration.GetConnectionString("DefaultConnection");
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadCountries();
    }

    private async Task LoadCountries()
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            var result = await connection.QueryAsync<CountryModel>("SELECT * FROM Country");
            countries = result.ToList();
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = $"Error loading countries: {ex.Message}";
        }
    }

    private async Task AddOrUpdateCountry()
    {
        try
        {
            using var connection = new SqlConnection(connectionString);

            if (isEditMode)
            {
                var query = "UPDATE Country SET EnglishName = @EnglishName, DariName = @DariName WHERE CountryId = @CountryId";
                await connection.ExecuteAsync(query, new { newCountry.EnglishName, newCountry.DariName, newCountry.CountryId });
            }
            else
            {
                var query = "INSERT INTO Country (EnglishName, DariName) VALUES (@EnglishName, @DariName)";
                await connection.ExecuteAsync(query, new { newCountry.EnglishName, newCountry.DariName });
            }

            isSuccess = true;
            isError = false;

            newCountry = new CountryModel();
            isEditMode = false;

            await LoadCountries();
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = $"An error occurred: {ex.Message}";
        }
    }

    private void EditCountry(CountryModel country)
    {
        isEditMode = true;
        newCountry = new CountryModel
            {
                CountryId = country.CountryId,
                EnglishName = country.EnglishName,
                DariName = country.DariName
            };
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newCountry = new CountryModel();
    }

    private void ConfirmDeleteCountry(int countryId)
    {
        countryIdToDelete = countryId;
        isDeleteConfirmationVisible = true;
    }

    private async Task DeleteCountry()
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            var query = "DELETE FROM Country WHERE CountryId = @CountryId";
            await connection.ExecuteAsync(query, new { CountryId = countryIdToDelete });

            await LoadCountries();

            isSuccess = true;
            isError = false;
            isDeleteConfirmationVisible = false;
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = $"An error occurred: {ex.Message}";
            isDeleteConfirmationVisible = false;
        }
    }

    private void CancelDelete()
    {
        isDeleteConfirmationVisible = false;
    }

    public class CountryModel
    {
        public int CountryId { get; set; }
        [Required(ErrorMessage = "Please enter a EnglishName.")]
        public string EnglishName { get; set; }
        [Required(ErrorMessage = "Please enter a DariName.")]
        public string DariName { get; set; }
    }
}
