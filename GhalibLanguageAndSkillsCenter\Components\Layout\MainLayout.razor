@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Authorization

<!DOCTYPE html>
<html lang="fa" dir="rtl">
 <!-- Set language to Persian and direction to RTL -->
<head>
    <!-- Add meta tags and other head content if needed -->
    
</head>

<body>

    <div class="mainlayout-page">
        <div class="mainlayout-sidebar" dir="rtl">
            <NavMenu />
        </div>

        <main class="mainlayout-main">
            <div class="mainlayout-top-row">

                <!-- AuthorizeView to show either "Hello, user!" or the About link -->
                <AuthorizeView Context="authState">
                    <Authorized>
                        <span class="mainlayout-nav-item">
                            سلام، @authState.User.Identity.Name!
                        </span>
                    </Authorized>
                </AuthorizeView>
                <AuthorizeView Roles="Administrator">
                    <NavLink class="mainlayout-nav-link mainlayout-text-primary" href="Account/Register">
                        <span class="bi bi-person-plus-fill me-2 mainlayout-nav-icon" aria-hidden="true"></span> ایجاد یوزر
                    </NavLink>
                </AuthorizeView>
                <!-- Login / Logout -->
                <AuthorizeView>

                        <Authorized>
                            <div class="mainlayout-nav-item">
                                <NavLink class="mainlayout-nav-link mainlayout-text-danger" href="Account/Logout">
                                    <span class="bi bi-box-arrow-right mainlayout-text-danger" aria-hidden="true"></span> <!-- Red logout icon -->
                                    خروج
                                </NavLink>
                            </div>
                    </Authorized>
                    <NotAuthorized>
                        <div class="mainlayout-nav-item">
                            <NavLink class="mainlayout-nav-link" href="Account/Login">
                                <span class="bi bi-person-badge-nav-menu" aria-hidden="true"></span>
                                ورود
                            </NavLink>
                        </div>
                    </NotAuthorized>
                </AuthorizeView>

            </div>

            <article class="mainlayout-content">
                @Body
            </article>
        </main>
    </div>

    <div id="blazor-error-ui" class="mainlayout-error-ui" data-nosnippet>
        یک خطای ناشناخته رخ داده است.
        <a href="." class="mainlayout-reload">بارگذاری مجدد</a>
        <span class="mainlayout-dismiss">🗙</span>
    </div>

</body>
</html>
