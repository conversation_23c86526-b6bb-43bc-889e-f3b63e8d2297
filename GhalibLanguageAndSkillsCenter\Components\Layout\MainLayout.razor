﻿@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Authorization

<!DOCTYPE html>
<html lang="fa" dir="rtl">
 <!-- Set language to Persian and direction to RTL -->
<head>
    <!-- Add meta tags and other head content if needed -->
</head>

<body>
    
    <div class="page">
        <div class="sidebar" dir="rtl">
            <NavMenu />
        </div>

        <main>
            <div class="top-row px-4">

                <!-- AuthorizeView to show either "Hello, user!" or the About link -->
                <AuthorizeView Context="authState">
                    <Authorized>
                        <span class="nav-item px-3">
                            سلام، @authState.User.Identity.Name!
                        </span>
                    </Authorized>
                </AuthorizeView>
                <AuthorizeView Roles="Administrator">
                    <NavLink class="nav-link text-primary " href="Account/Register">
                        <span class="bi bi-person-plus-fill me-2 nav-icon" aria-hidden="true"></span> ایجاد یوزر
                    </NavLink>
                </AuthorizeView>
                <!-- Login / Logout -->
                <AuthorizeView>
                  
                        <Authorized>
                            <div class="nav-item me-3">
                                <NavLink class="nav-link text-danger" href="Account/Logout">
                                    <span class="bi bi-box-arrow-right text-danger" aria-hidden="true"></span> <!-- Red logout icon -->
                                    خروج 
                                </NavLink>
                            </div>
                    </Authorized>
                    <NotAuthorized>
                        <div class="nav-item px-3">
                            <NavLink class="nav-link" href="Account/Login">
                                <span class="bi bi-person-badge-nav-menu" aria-hidden="true"></span>
                                ورود
                            </NavLink>
                        </div>
                    </NotAuthorized>
                </AuthorizeView>

            </div>

            <article class="content px-4">
                @Body
            </article>
        </main>
    </div>
    
    <div id="blazor-error-ui" data-nosnippet>
        یک خطای ناشناخته رخ داده است.
        <a href="." class="reload">بارگذاری مجدد</a>
        <span class="dismiss">🗙</span>
    </div>
    
</body>
</html>
