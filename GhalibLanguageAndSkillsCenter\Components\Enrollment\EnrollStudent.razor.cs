﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Authorization;

using GhalibLanguageAndSkillsCenter.Services;
using GhalibLanguageAndSkillsCenter.Models.Enrollment;
using GhalibLanguageAndSkillsCenter.Utilities;

namespace GhalibLanguageAndSkillsCenter.Components.Enrollment;

[Authorize]
public partial class EnrollStudent : ComponentBase
{
    [Inject] private IGenericRepository<object> repository { get; set; }
    [Inject] private IGenericRepository<ProgramModel> programRepository { get; set; }

    private EnrollStudentModel enrollmentModel = new();
    private List<ProgramModel> programs = new();
    private string successMessage;
    private string errorMessage;
    private string enrollDate;

    protected override async Task OnInitializedAsync()
    {
        programs = (await programRepository.GetAllAsync("GetAllPrograms")).ToList();
    }

    private void OnSectionChanged(int id)
    {
        enrollmentModel.SectionId = id;
    }
    private void OnProgramChange(int id)
    {
        enrollmentModel.ProgramId = id;
    }
    private void OnShiftChange(int id)
    {
        enrollmentModel.ShiftId = id;
    }
    private async Task HandleEnroll()
    {
        enrollmentModel.EnrollmentDate = PersianDateConverter.ToDateTime(enrollDate);
        if (enrollmentModel.ProgramId == 0 || enrollmentModel.SectionId == 0)
        {
            errorMessage = "Please select a Program and Section.";
            successMessage = null;
            return;
        }

        try
        {
            await repository.AddAsync("RegisterStudentAndEnroll", new
            {
                enrollmentModel.Name,
                enrollmentModel.LastName,
                enrollmentModel.FatherName,
                enrollmentModel.Contact,
                enrollmentModel.ProgramId,
                enrollmentModel.SectionId,
                enrollmentModel.ShiftId,
                enrollmentModel.EnrollmentDate
            });

            successMessage = "Student successfully registered and enrolled!";
            errorMessage = null;
            enrollmentModel = new(); // Clear form
        }
        catch (Exception ex)
        {
            successMessage = null;
            errorMessage = $"An error occurred: {ex.Message}";
        }
    }



}
