﻿/* MainLayout Styles - Modern Light Theme */
.mainlayout-page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8fafc;
}

.mainlayout-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 280px;
    z-index: 1000;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.mainlayout-main {
    margin-right: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #ffffff;
}

.mainlayout-top-row {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 1.5rem;
    direction: rtl;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    min-height: 70px;
}

.mainlayout-nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    color: #475569;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mainlayout-nav-item:hover {
    background-color: #f1f5f9;
    transform: translateY(-1px);
}

.mainlayout-nav-link {
    color: #3b82f6;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    border: 1px solid transparent;
}

.mainlayout-nav-link:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    border-color: #3b82f6;
}

.mainlayout-text-primary {
    color: #3b82f6 !important;
}

.mainlayout-text-danger {
    color: #ef4444 !important;
}

.mainlayout-nav-icon {
    margin-left: 0.75rem;
    font-size: 1.2rem;
}

.mainlayout-content {
    flex: 1;
    padding: 2.5rem;
    background-color: #ffffff;
    min-height: calc(100vh - 70px);
    border-radius: 12px 0 0 0;
    margin-top: -12px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.mainlayout-error-ui {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    bottom: 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    display: none;
    left: 0;
    padding: 1rem 2rem;
    position: fixed;
    width: 100vw;
    z-index: 1000;
    direction: rtl;
    border-top: 3px solid #f59e0b;
}

.mainlayout-error-ui .mainlayout-reload {
    color: #dc2626;
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 1rem;
    background-color: #ffffff;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.mainlayout-error-ui .mainlayout-reload:hover {
    background-color: #dc2626;
    color: #ffffff;
}

.mainlayout-error-ui .mainlayout-dismiss {
    cursor: pointer;
    position: absolute;
    left: 1rem;
    top: 1rem;
    background-color: #ffffff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #6b7280;
    transition: all 0.3s ease;
}

.mainlayout-error-ui .mainlayout-dismiss:hover {
    background-color: #ef4444;
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mainlayout-sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
        width: 100%;
    }

    .mainlayout-main {
        margin-right: 0;
    }

    .mainlayout-sidebar.show {
        transform: translateX(0);
    }

    .mainlayout-content {
        padding: 1.5rem;
        margin-top: 0;
        border-radius: 0;
    }

    .mainlayout-top-row {
        padding: 1rem;
    }
}