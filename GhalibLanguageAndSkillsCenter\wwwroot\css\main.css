﻿/* MainLayout Styles */
.mainlayout-page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.mainlayout-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 260px;
    z-index: 1000;
    background-color: #1f1f2e;
    overflow-y: auto;
}

.mainlayout-main {
    margin-right: 260px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.mainlayout-top-row {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 1rem;
    direction: rtl;
}

.mainlayout-nav-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: #495057;
    font-weight: 500;
}

.mainlayout-nav-link {
    color: #007bff;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.mainlayout-nav-link:hover {
    background-color: #e9ecef;
    color: #0056b3;
}

.mainlayout-text-primary {
    color: #007bff !important;
}

.mainlayout-text-danger {
    color: #dc3545 !important;
}

.mainlayout-nav-icon {
    margin-left: 0.5rem;
    font-size: 1.1rem;
}

.mainlayout-content {
    flex: 1;
    padding: 2rem;
    background-color: #ffffff;
    min-height: calc(100vh - 80px);
}

.mainlayout-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100vw;
    z-index: 1000;
    direction: rtl;
}

.mainlayout-error-ui .mainlayout-reload {
    color: red;
    text-decoration: none;
    font-weight: bold;
}

.mainlayout-error-ui .mainlayout-dismiss {
    cursor: pointer;
    position: absolute;
    left: 0.75rem;
    top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mainlayout-sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .mainlayout-main {
        margin-right: 0;
    }

    .mainlayout-sidebar.show {
        transform: translateX(0);
    }
}