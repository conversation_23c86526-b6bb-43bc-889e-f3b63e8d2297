﻿@page "/timetable"
@inject IGenericRepository<TimeTableModel> TimeTableRepo
@rendermode InteractiveServer
<div class="d-flex justify-content-between align-items-center mb-3">
    <h3>TimeTables</h3>
    <a href="/createtimetable" class="btn btn-dark">اضافه +</a>
</div>


<div class="mb-3">
    <label>Select Program</label>
    <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram SelectedProgramId="@SelectedProgramId"
                                                                                SelectedProgramIdChanged="@OnProgramChanged" />
</div>

<div class="mb-3">
    <label>Select Section</label>
    <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectSection SelectedSectionId="@SelectedSectionId"
                                                                                SelectedSectionIdChanged="@OnSectionChanged" />
</div>

@if (SelectedProgramId > 0 && SelectedSectionId > 0)
{
    if (result.Any())
    {
        <div class="table-responsive">
            <table class="table table-bordered text-center align-middle">
                <thead class="table-light">
                    <tr>
                        @foreach (var day in DaysOfWeek)
                        {
                            <th>@day</th>
                        }
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        @foreach (var day in DaysOfWeek)
                        {
                            var entries = result
                            .Where(e => e.DayOfWeek.Equals(day, StringComparison.OrdinalIgnoreCase))
                            .OrderBy(e => e.StartTime)
                            .ToList();

                            <td>
                                @if (entries.Any())
                                {
                                    @foreach (var entry in entries)
                                    {
                                        <div>
                                            <strong>@entry.SubjectName</strong><br />
                                            <small>@entry.StartTime تا @entry.EndTime</small>
                                            <hr />
                                        </div>
                                    }
                                }
                                else
                                {
                                    <span class="text-muted">No classes</span>
                                }
                            </td>
                        }
                    </tr>
                </tbody>
            </table>
        </div>
    }
    else
    {
        <p class="text-muted">No timetable entries found for the selected Program and Section.</p>
    }
}
else
{
    <p class="text-danger">Please select a Program and a Section to view the timetable.</p>
}

@code {
    private int SelectedProgramId;
    private int SelectedSectionId;
    private List<TimeTableModel> result = new();

    private readonly List<string> DaysOfWeek = new()
    {
        "Saturday", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday"
    };

    private async Task OnProgramChanged(int id)
    {
        SelectedProgramId = id;
        await LoadTimeTables();
    }

    private async Task OnSectionChanged(int id)
    {
        SelectedSectionId = id;
        await LoadTimeTables();
    }

    private async Task LoadTimeTables()
    {
        if (SelectedProgramId > 0 && SelectedSectionId > 0)
        {
            result = (await TimeTableRepo.GetAllAsyncById("GetTimeTableByProgramAndSection",
                new { ProgramId = SelectedProgramId, SectionId = SelectedSectionId })).ToList();
        }
    }
}
