﻿@page "/reports"
@inject IGenericRepository<StudentPaymentReportModel> PaymentReportRepo
@rendermode InteractiveServer

@attribute [Authorize]


<!-- Payment Report Section -->
<div class="mb-5">
    <h3>Students Payment Report</h3>
    <div class="form-group">
        <label>Select Program for Report</label>

        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram SelectedProgramId="@reportProgramId"
                                         SelectedProgramIdChanged="OnReportProgramChange" />
        <button class="btn btn-primary mt-2" @onclick="LoadPaymentReport">Load Report</button>
    </div>

    @if (paymentReports is not null)
    {
        <table class="table table-bordered mt-3">
            <thead>
                <tr>
                    <th>Student Name</th>
                    <th>Program</th>
                    <th>Original Fee</th>
                    <th>Discount %</th>
                    <th>GeneralDiscount (%)</th>
                    <th>Final Fee</th>
                    <th>Paid Amount</th>
                    <th>Remaining</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var rpt in paymentReports)
                {
                    <tr>
                        <td>@rpt.StudentName</td>
                        <td>@rpt.ProgramName</td>
                        <td>@rpt.OriginalFee</td>
                        <td>@rpt.DiscountPercentage</td>
                        <td>@rpt.GeneralDiscountPercentage</td>
                        <td>@rpt.FinalFee</td>
                        <td>@rpt.PaidAmount</td>
                        <td>@rpt.RemainingAmount</td>
                    </tr>
                }
            </tbody>
        </table>
    }
</div>

<hr />


@code {

    private int reportProgramId;
    private List<StudentPaymentReportModel> paymentReports;

    private void OnReportProgramChange(int programId)
    {
        reportProgramId = programId;
    }

    private async Task LoadPaymentReport()
    {
        paymentReports = (await PaymentReportRepo.GetAllAsyncById("GetPaymentsByProgramId", new { ProgramId = reportProgramId })).ToList();
    }
    public class StudentPaymentReportModel
    {
        public string StudentName { get; set; }
        public string ProgramName { get; set; }
        public int OriginalFee { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal GeneralDiscountPercentage { get; set; }
        public int FinalFee { get; set; }
        public int PaidAmount { get; set; }
        public int RemainingAmount { get; set; }
    }

    // ... existing methods ...
}
