﻿
@using GhalibLanguageAndSkillsCenter.Models.Payment
@inject IGenericRepository<StudentPaymentModel> historyRepository
@inject NavigationManager Nav
@rendermode InteractiveServer

<div class="shadow-lg p-4 mt-4">

    @if (history == null)
    {
        <p>Loading…</p>
    }
    else if (!history.Any())
    {
        <p><em>No payment history found.</em></p>
    }
    else
    {
        <table class="table table-striped table-hover">
            <thead class="thead-light">
                <tr>
                    <th>Installment</th>
                    <th>Date</th>
                    <th>Program</th>
                    <th>Orig. Fee</th>
                    <th>Discount (%)</th>
                    <th>General Discount (%)</th>
                    <th>Final Fee</th>
                    <th>Paid</th>
                    <th>Total Paid</th>
                    <th>Remaining</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in history)
                {
                    <tr>
                        <td>@item.Installment</td>
                        <td>@PersianDateConverter.ToPersianString(item.PaymentDate)</td>
                        <td>@item.ProgramName</td>
                        <td>@item.OriginalFee.ToString("F2")</td>
                        <td>@item.DiscountPercentage.ToString("F2")</td>
                        <td>@item.GeneralDiscountPercentage.ToString("F2")</td>
                        <td>@item.FinalFee.ToString("F2")</td>
                        <td>@item.PaidAmount.ToString("F2")</td>
                        <td>@item.TotalPaid.ToString("F2")</td>
                        <td>@item.RemainingAmount.ToString("F2")</td>
                    </tr>
                }
            </tbody>
        </table>
    }
</div>

@code {
    [Parameter]
    public int StudentId { get; set; }
    [Parameter]

    public int ProgramId { get; set; }

    [Parameter]
    public int RefereshToken { get; set; }
    private List<StudentPaymentModel> history;

    protected override async Task OnParametersSetAsync()
    {
        

        history = (await historyRepository
            .GetAllAsyncById("GetStudentPaymentHistory", new { StudentId }))
            .Where(p => p.ProgramId == ProgramId)
        .ToList();
    }
    
   

}
