﻿@page "/province"
@using System.ComponentModel.DataAnnotations
@inject IGenericRepository<ProvinceModel> provinceRepository
@inject IGenericRepository<CountryModel> countryRepository
@rendermode InteractiveServer
@attribute [Authorize]
<h3>Province</h3>

<EditForm Model="@newProvince" OnValidSubmit="HandleSubmit" FormName="province">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label>English Name</label>
        <InputText class="form-control" @bind-Value="newProvince.EnglishName" />
    </div>

    <div class="form-group">
        <label>Dari Name</label>
        <InputText class="form-control" @bind-Value="newProvince.DariName" />
    </div>

    <div class="form-group">
        <label>Select Country</label>
        <InputSelect class="form-control" @bind-Value="newProvince.CountryId">
            <option value="">-- Select Country --</option>
            @foreach (var country in countries)
            {
                <option value="@country.CountryId">@country.EnglishName</option>
            }
        </InputSelect>
    </div>

    <button class="btn btn-primary mt-2" type="submit">
        @(isEditMode ? "Update Province" : "Add Province")
    </button>

    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary mt-2 ms-2" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>


<hr />

<h3>Provinces List</h3>

@if (provinces is not null && provinces.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Dari Name</th>
                <th>Country</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var province in provinces)
            {
                <tr>
                    <td>@province.ProvinceId</td>
                    <td>@province.EnglishName</td>
                    <td>@province.DariName</td>
                    <td>@(countries.FirstOrDefault(c => c.CountryId == province.CountryId)?.EnglishName)</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditProvince(province)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => DeleteProvince(province.ProvinceId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No provinces found.</p>
}

@code {
    private ProvinceModel newProvince = new();
    private List<ProvinceModel> provinces = new();
    private List<CountryModel> countries = new();
    private bool isEditMode = false;

    protected override async Task OnInitializedAsync()
    {
        countries = (await countryRepository.GetAllAsync("GetCountries")).ToList();
        await LoadProvinces();
    }

    private async Task LoadProvinces()
    {
        provinces = (await provinceRepository.GetAllAsync("GetAllProvinces")).ToList();
    }

    private async Task HandleSubmit()
    {
        if (newProvince.CountryId == 0)
        {
            // show an error maybe
            return;
        }

        if (isEditMode)
        {
            await provinceRepository.UpdateAsync("UpdateProvince", new
            {
                newProvince.ProvinceId,
                newProvince.EnglishName,
                newProvince.DariName,
                CountryId = newProvince.CountryId
            });
        }
        else
        {
            await provinceRepository.AddAsync("AddProvince", new
            {
                newProvince.EnglishName,
                newProvince.DariName,
                CountryId = newProvince.CountryId
            });
        }

        newProvince = new();
        isEditMode = false;
        await LoadProvinces();
    }

    private void EditProvince(ProvinceModel province)
    {
        isEditMode = true;
        newProvince = new ProvinceModel
            {
                ProvinceId = province.ProvinceId,
                EnglishName = province.EnglishName,
                DariName = province.DariName,
                CountryId = province.CountryId
            };
    }

    private async Task DeleteProvince(int id)
    {
        await provinceRepository.DeleteAsync("DeleteProvince", new { ProvinceId = id });
        await LoadProvinces();
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newProvince = new();
    }

    public class ProvinceModel
    {
        public int ProvinceId { get; set; }
        [Required(ErrorMessage = "Please enter a EnglishName.")]
        public string EnglishName { get; set; }
        [Required(ErrorMessage = "Please enter a DariName.")]
        public string DariName { get; set; }
        [Required(ErrorMessage = "Please Select a Country.")]
        public int CountryId { get; set; }
    }

    public class CountryModel
    {
        public int? CountryId { get; set; }
        public string EnglishName { get; set; }
    }
}
