﻿@page "/createtimetable"
@inject IGenericRepository<string> TimeTableRepo
@inject NavigationManager Navigation
@rendermode InteractiveServer
<h3>Create Time Table</h3>

<EditForm Model="@model" OnValidSubmit="HandleValidSubmit">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="mb-3">
        <label>Day of Week</label>
        <InputSelect class="form-control" @bind-Value="model.DayOfWeek">
            <option value="">Select a day</option>
            @foreach (var day in DaysOfWeek)
            {
                <option value="@day">@day</option>
            }
        </InputSelect>
    </div>

    <div class="form-group">
        <label>Start Time</label>
        <InputText class="form-control" @bind-Value="model.StartTime" type="time" />
    </div>

    <div class="form-group">
        <label>End Time</label>
        <InputText class="form-control" @bind-Value="model.EndTime" type="time" />
    </div>

    <div class="mb-3">
        <label>Program</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram 
            SelectedProgramId="@model.ProgramId" 
            SelectedProgramIdChanged="OnProgramChanged" />
    </div>

    <div class="mb-3">
        <label>Section</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectSection 
            SelectedSectionId="@model.SectionId" 
            SelectedSectionIdChanged="OnSectionChanged" />
    </div>

    <div class="mb-3">
        <label>Subject</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectSubject 
            SelectedSubjectId="@model.SubjectId" 
            SelectedSubjectIdChanged="OnSubjectChanged" 
            ProgramId="@model.ProgramId" />
    </div>

    <button class="btn btn-primary" type="submit">Create</button>
</EditForm>

@code {
    private TimeTableModel model = new();

    private readonly List<string> DaysOfWeek = new()
    {
        "Saturday", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday"
    };

    private async Task HandleValidSubmit()
    {
        var parameters = new
        {
            model.DayOfWeek,
            model.StartTime, // Automatically uses TimeOnly
            model.EndTime,   // Automatically uses TimeOnly
            model.SubjectId,
            model.ProgramId,
            model.SectionId
        };

        await TimeTableRepo.AddAsync("AddTimeTable", parameters);
        Navigation.NavigateTo("/timetable");
    }

    private Task OnProgramChanged(int id)
    {
        model.ProgramId = id;
        model.SubjectId = 0; // reset subject
        return Task.CompletedTask;
    }

    private Task OnSectionChanged(int id)
    {
        model.SectionId = id;
        return Task.CompletedTask;
    }

    private Task OnSubjectChanged(int id)
    {
        model.SubjectId = id;
        return Task.CompletedTask;
    }
}
