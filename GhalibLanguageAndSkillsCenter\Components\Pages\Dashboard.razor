﻿@page "/dashboard"
@inject IGenericRepository<DashboardStatsModel> dashboardRepo
@inject IGenericRepository<ProgramModel> programRepo
@inject IGenericRepository<ActivityModel> activityRepo

<div class="dashboard-container fade-in">
    <h3 class="mb-4">Dashboard</h3>

    @if (isLoading)
    {
        <p>Loading...</p>
    }
    else
    {
        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card dashboard-card text-white bg-primary shadow">
                    <div class="card-body">
                        <h5 class="card-title">Active Programs</h5>
                        <p class="card-text display-6 fw-bold">@dashboardData.ActiveProgramsCount</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card dashboard-card text-white bg-success shadow">
                    <div class="card-body">
                        <h5 class="card-title">Total Staff</h5>
                        <p class="card-text display-6 fw-bold">@dashboardData.StaffCount</p>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card dashboard-card text-white bg-info shadow">
                    <div class="card-body">
                        <h5 class="card-title">Enrolled Students</h5>
                        <p class="card-text display-6 fw-bold">@dashboardData.EnrolledStudentsInActivePrograms</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card dashboard-card border-0 shadow mb-5">
            <div class="card-body">
                <h4 class="card-title mb-4">Upcoming Program Deadlines</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <h5 class="text-primary">Starting in Next 2 Weeks</h5>
                            @if (startingSoon.Any())
                            {
                                <ul class="list-group list-group-flush">
                                    @foreach (var p in startingSoon)
                                    {
                                        <li class="list-group-item dashboard-deadline-item d-flex justify-content-between align-items-center">
                                            <span>@p.Name</span>
                                            <span class="badge dashboard-badge bg-primary rounded-pill">@p.StartingDate.ToString("MMM dd")</span>
                                        </li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <p class="text-muted fst-italic">None</p>
                            }
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <h5 class="text-danger">Ending in Next 2 Weeks</h5>
                            @if (endingSoon.Any())
                            {
                                <ul class="list-group list-group-flush">
                                    @foreach (var p in endingSoon)
                                    {
                                        <li class="list-group-item dashboard-deadline-item d-flex justify-content-between align-items-center">
                                            <span>@p.Name</span>
                                            <span class="badge dashboard-badge bg-danger rounded-pill">@p.EndingDate.ToString("MMM dd")</span>
                                        </li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <p class="text-muted fst-italic">None</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card dashboard-card shadow mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Recent Payments</h5>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Student</th>
                                    <th>Program</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var activity in recentPayments)
                                {
                                    <tr class="dashboard-table-row">
                                        <td>@PersianDateConverter.ToPersianString(activity.ActivityDate)</td>
                                        <td>@activity.Student</td>
                                        <td>@activity.Program</td>
                                        <td>@activity.AmountPaid</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card dashboard-card shadow mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Recent Enrollments</h5>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Student</th>
                                    <th>Program</th>
                                    <th>Shift</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var activity in recentEnrollments)
                                {
                                    <tr class="dashboard-table-row">
                                        <td>@PersianDateConverter.ToPersianString(activity.ActivityDate)</td>
                                        <td>@activity.Student</td>
                                        <td>@activity.Program</td>
                                        <td>@activity.ShiftName</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private DashboardStatsModel dashboardData = new();
    private List<ProgramModel> programs = new();
    private List<ProgramModel> startingSoon = new();
    private List<ProgramModel> endingSoon = new();
    private List<ActivityModel> recentPayments = new();
    private List<ActivityModel> recentEnrollments = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        dashboardData = await dashboardRepo.GetByIdAsync("DashboardsProcedure", null);
        programs = (await programRepo.GetAllAsync("GetAllPrograms")).ToList();

        var today = DateTime.Today;
        var cutoff = today.AddDays(14);

        startingSoon = programs
            .Where(p => p.IsActive == 1 && p.StartingDate.Date >= today && p.StartingDate.Date <= cutoff)
            .OrderBy(p => p.StartingDate)
            .ToList();

        endingSoon = programs
            .Where(p => p.IsActive == 1 && p.EndingDate.Date >= today && p.EndingDate.Date <= cutoff)
            .OrderBy(p => p.EndingDate)
            .ToList();

        var allActivities = (await activityRepo.GetAllAsync("GetRecentActivities")).ToList();
        recentPayments = allActivities.Where(a => a.ActivityType == "Payment").ToList();
        recentEnrollments = allActivities.Where(a => a.ActivityType == "Enrollment").ToList();

        isLoading = false;
    }

    public class DashboardStatsModel
    {
        public int ActiveProgramsCount { get; set; }
        public int StaffCount { get; set; }
        public int EnrolledStudentsInActivePrograms { get; set; }
    }

    public class ProgramModel
    {
        public int ProgramId { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime StartingDate { get; set; }
        public DateTime EndingDate { get; set; }
        public int IsActive { get; set; }
    }

    public class ActivityModel
    {
        public string ActivityType { get; set; } = string.Empty;
        public DateTime ActivityDate { get; set; }
        public string Student { get; set; } = string.Empty;
        public string Program { get; set; } = string.Empty;
        public string ShiftName { get; set; } = string.Empty;
        public decimal AmountPaid { get; set; }
    }
}
