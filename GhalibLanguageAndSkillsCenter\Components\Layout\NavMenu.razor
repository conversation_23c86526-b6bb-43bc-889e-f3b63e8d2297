﻿@implements IDisposable
@inject NavigationManager NavigationManager
@rendermode InteractiveServer


<div class="sidebar-page">
    <div class="sidebar-top-row">
        <div class="container-fluid">
            <a class="sidebar-navbar-brand" href="">مرکز زبان و مهارت‌های غالب</a>
        </div>
    </div>

    <input type="checkbox" title="منوی ناوبری" class="sidebar-navbar-toggler" />

    <div class="sidebar-nav-scrollable" onclick="document.querySelector('.sidebar-navbar-toggler').click()">
        <nav class="sidebar-nav">

            <!-- Home -->
            <div class="sidebar-nav-item">
                <NavLink class="sidebar-nav-link" href="" Match="NavLinkMatch.All">
                    <span class="bi bi-house-door sidebar-nav-icon"></span> خانه
                </NavLink>
            </div>

            <!-- Enrollment Group -->
            <div class="sidebar-nav-item">
                <button type="button" class="sidebar-nav-link d-flex justify-content-between align-items-center" @onclick="ToggleEnrollment">
                    <span><span class="bi bi-journal-plus sidebar-nav-icon"></span> پذیزش</span>
                    <span class="bi" style="transform: rotate(@(showEnrollment ? 90 : 0)deg); transition: transform .2s;">&#9656;</span>
                </button>
                @if (showEnrollment)
                {
                    <ul class="sidebar-submenu">
                        <li class="sidebar-nav-item">
                            <NavLink class="sidebar-nav-link" href="student" Match="NavLinkMatch.Prefix">شاگردان</NavLink>
                        </li>
                        <li class="sidebar-nav-item">
                            <NavLink class="sidebar-nav-link" href="enroll" Match="NavLinkMatch.Prefix"> ثبت نام</NavLink>
                        </li>
                    </ul>
                }
            </div>

            <!-- Finance Group -->
            <div class="sidebar-nav-item">
                <button type="button" class="sidebar-nav-link d-flex justify-content-between align-items-center" @onclick="ToggleFinance">
                    <span><span class="bi bi-cash-stack sidebar-nav-icon"></span> مالی</span>
                    <span class="bi" style="transform: rotate(@(showFinance ? 90 : 0)deg); transition: transform .2s;">&#9656;</span>
                </button>
                @if (showFinance)
                {
                    <ul class="sidebar-submenu">
                        <li class="sidebar-nav-item">
                            <NavLink class="sidebar-nav-link" href="payment" Match="NavLinkMatch.Prefix">فیس</NavLink>
                        </li>
                        <li class="sidebar-nav-item">
                            <NavLink class="sidebar-nav-link" href="reports" Match="NavLinkMatch.Prefix">گزارشات شاگردان</NavLink>
                        </li>
                    </ul>
                }
            </div>

            <!-- Timetable -->
            <div class="sidebar-nav-item">
                <NavLink class="sidebar-nav-link" href="timetable">
                    <span class="bi bi-clock sidebar-nav-icon"></span> تقسیم اوقات
                </NavLink>
            </div>

            <!-- Staff -->
            <div class="sidebar-nav-item">
                <NavLink class="sidebar-nav-link" href="staff">
                    <span class="bi bi-person-badge sidebar-nav-icon"></span> مدیریت کارمندان
                </NavLink>
            </div>



            <!-- Settings -->
            <div class="sidebar-nav-item">
                <NavLink class="sidebar-nav-link" href="settings">
                    <span class="bi bi-gear-fill sidebar-nav-icon"></span> تنظیمات
                </NavLink>
            </div>

        </nav>
    </div>
</div>

@code {
    private bool showEnrollment;
    private bool showFinance;
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
        UpdateMenuState(currentUrl);
    }

    private void ToggleEnrollment()
    {
        showEnrollment = !showEnrollment;
        showFinance = false;
    }

    private void ToggleFinance()
    {
        showFinance = !showFinance;
        showEnrollment = false;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        UpdateMenuState(currentUrl);
        StateHasChanged();
    }

    private void UpdateMenuState(string? url)
    {
        // Collapse all by default
        showEnrollment = false;
        showFinance = false;

        if (!string.IsNullOrEmpty(url))
        {
            if (url.StartsWith("student") || url.StartsWith("enroll"))
            {
                showEnrollment = true;
            }
            else if (url.StartsWith("payment") || url.StartsWith("reports"))
            {
                showFinance = true;
            }
        }
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}
