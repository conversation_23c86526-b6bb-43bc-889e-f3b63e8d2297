﻿@page "/settings"
@inject NavigationManager Navigation
@rendermode InteractiveServer
@attribute [Authorize]

<h3 class="mb-4 text-center fw-bold">Settings</h3>

<div class="container">
    <div class="row g-4 justify-content-center">
        @foreach (var setting in settings)
        {
            <div class="col-md-4">
                <div class="card shadow border-0 h-100 text-white bg-gradient @setting.BgColor" style="cursor: pointer;" @onclick="setting.OnClick">
                    <div class="card-body text-center">
                        <i class="@setting.Icon mb-3" style="font-size: 2rem;"></i>
                        <h5 class="card-title">@setting.Title</h5>
                        <p class="card-text">@setting.Description</p>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@code {
    private List<SettingCard> settings;

    protected override void OnInitialized()
    {
        settings = new()
        {
            new SettingCard("Manage Departments", "Add, edit, or delete departments.", "bg-info", "bi bi-building", GoToDepartment),
            new SettingCard("Manage Countries", "Add, edit, or delete countries.", "bg-dark", "bi bi-globe", GoToCountry),
            new SettingCard("Manage Positions", "Add, edit, or delete positions.", "bg-primary", "bi bi-briefcase", GoToPosition),
            new SettingCard("Manage Provinces", "Add, edit, or delete provinces.", "bg-success", "bi bi-map", GoToProvince),
            new SettingCard("Manage Programs", "Add, edit, or delete programs.", "bg-secondary", "bi bi-journal-code", GoToProgram),
            new SettingCard("Manage Subjects", "Add, edit, or delete subjects.", "bg-dark text-dark", "bi bi-book", GoToSubject),
            new SettingCard("Account Settings", "Control your account settings.", "bg-warning text-dark", "bi bi-gear", GoToSettings)
        };
    }

    private void GoToDepartment() => Navigation.NavigateTo("/department");
    private void GoToCountry() => Navigation.NavigateTo("/country");
    private void GoToPosition() => Navigation.NavigateTo("/Position");
    private void GoToProvince() => Navigation.NavigateTo("/province");
    private void GoToProgram() => Navigation.NavigateTo("/program");
    private void GoToSubject() => Navigation.NavigateTo("/subjects");
    private void GoToSettings() => Navigation.NavigateTo("/Account/Manage");

    private class SettingCard
    {
        public string Title { get; }
        public string Description { get; }
        public string BgColor { get; }
        public string Icon { get; }
        public EventCallback OnClick { get; }

        public SettingCard(string title, string desc, string color, string icon, Action onClick)
        {
            Title = title;
            Description = desc;
            BgColor = color;
            Icon = icon;
            OnClick = EventCallback.Factory.Create(this, onClick);
        }
    }
}
