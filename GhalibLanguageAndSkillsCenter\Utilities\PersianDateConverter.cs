﻿using System.Globalization;

namespace GhalibLanguageAndSkillsCenter.Utilities
{
    public class PersianDateConverter
    {
        private static readonly PersianCalendar _calendar = new();

        /// <summary>
        /// Converts a Persian date string (yyyy/MM/dd) into a Gregorian DateTime.
        /// Handles Persian digits automatically.
        /// </summary>
        public static DateTime ToDateTime(string persianDate)
        {
            if (string.IsNullOrWhiteSpace(persianDate))
                throw new ArgumentException("Date string is null or empty", nameof(persianDate));

            // Normalize Persian digits to ASCII
            string ascii = persianDate
                .Select(c => c switch
                {
                    '۰' => '0',
                    '۱' => '1',
                    '۲' => '2',
                    '۳' => '3',
                    '۴' => '4',
                    '۵' => '5',
                    '۶' => '6',
                    '۷' => '7',
                    '۸' => '8',
                    '۹' => '9',
                    _ => c
                })
                .Aggregate(string.Empty, (s, c) => s + c);

            var parts = ascii.Split('/');
            if (parts.Length != 3)
                throw new FormatException("Invalid Persian date format. Expected yyyy/MM/dd.");

            int year = int.Parse(parts[0]);
            int month = int.Parse(parts[1]);
            int day = int.Parse(parts[2]);

            return _calendar.ToDateTime(year, month, day, 0, 0, 0, 0);
        }

        /// <summary>
        /// Converts a Gregorian DateTime into a Persian date string (yyyy/MM/dd).
        /// </summary>
        public static string ToPersianString(DateTime dateTime)
        {
            return $"{_calendar.GetYear(dateTime):0000}/" +
                   $"{_calendar.GetMonth(dateTime):00}/" +
                   $"{_calendar.GetDayOfMonth(dateTime):00}";
        }
    }
}

