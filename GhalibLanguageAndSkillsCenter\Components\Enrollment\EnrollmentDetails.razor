﻿@page "/"
@inject IGenericRepository<EnrollmentDetailsModel> repository
@inject IGenericRepository<ProgramModel> programRepository
@inject IGenericRepository<SectionModel> sectionRepository
@inject IGenericRepository<ShiftModel> shiftRepository
@rendermode InteractiveServer
@inject IJSRuntime JS
@using TabBlazor
@using TabBlazor.Components
@using Align = TabBlazor.Align
@using TabBlazor.Services

@inject ToastService ToastService
@attribute [Authorize]

<h3 class="mb-4">Enrollment Details</h3>

<TabBlazor.ToastContainer></TabBlazor.ToastContainer>

@if (enrollmentDetails != null && enrollmentDetails.Any())
{
    <Table Item="EnrollmentDetailsModel"
           Items="enrollmentDetails"
           PageSize="10"
           ShowCheckboxes="false"
           MultiSelect="false"
           Hover="true"
           Responsive="true"
           @ref="table"
           >

        <HeaderTemplate>
            <strong>Enrollments</strong>
        </HeaderTemplate>

        <ChildContent>
            <Column Item="EnrollmentDetailsModel"
                    Property="e => e.StudentName"
                    Title="Student Name"
                    Searchable Sortable />

            <Column Item="EnrollmentDetailsModel"
                     Property="e => e.EnrollmentDate"
                     Title="Enrollment Date"
                     Searchable Sortable>
                <Template Context="detail">
                    @PersianDateConverter.ToPersianString(detail.EnrollmentDate)
                </Template>
            </Column>




            <Column Item="EnrollmentDetailsModel"
                    Property="e => e.ProgramName"
                    Title="Program"
                    Searchable Sortable>
                <EditorTemplate Context="detail">
                    <InputSelect class="form-control" @bind-Value="detail.ProgramId">
                        <option value="">-- Select Program --</option>
                        @foreach (var p in programs)
                        {
                            <option value="@p.ProgramId">@p.Name</option>
                        }
                    </InputSelect>
                </EditorTemplate>
                <Template Context="detail">
                    @detail.ProgramName
                </Template>
            </Column>

            <Column Item="EnrollmentDetailsModel"
                    Property="e => e.SectionName"
                    Title="Section"
                    Searchable Sortable>
                <EditorTemplate Context="detail">
                    <InputSelect class="form-control" @bind-Value="detail.SectionId">
                        <option value="">-- Select Section --</option>
                        @foreach (var sec in sections)
                        {
                            <option value="@sec.SectionId">@sec.Name</option>
                        }
                    </InputSelect>
                </EditorTemplate>
                <Template Context="detail">
                    @detail.SectionName
                </Template>
            </Column>

            <Column Item="EnrollmentDetailsModel"
                    Property="e => e.ShiftName"
                    Title="Shift"
                    Searchable Sortable>
                <EditorTemplate Context="detail">
                    <InputSelect class="form-control" @bind-Value="detail.ShiftId">
                        <option value="">-- Select Shift --</option>
                        @foreach (var sh in shifts)
                        {
                            <option value="@sh.ShiftId">@sh.Name</option>
                        }
                    </InputSelect>
                </EditorTemplate>
                <Template Context="detail">
                    @detail.ShiftName
                </Template>
            </Column>

            <Column Item="EnrollmentDetailsModel"
                    Property="e => e.CalculatedDuration"
                    Title="Duration (days)"
                    Align="Align.Start" />

            <Column Item="EnrollmentDetailsModel" Title="" ActionColumn Width="130px">
                <Template Context="detail">
                    <div class="d-flex">
                    <button class="btn btn-primary" @onclick="() => table.EditItem(detail)" @onclick:stopPropagation>
                       Edit
                    </button>
                    <Button BackgroundColor="TablerColor.Danger"
                            Size="ButtonSize.Small"
                            class="ms-2"
                            @onclick="() => DeleteEnrollment(detail.EnrollmentId)">
                        Delete
                    </Button>
                    </div>
                </Template>
                <EditorTemplate Context="detail">
                    <!-- Save Button -->
                    <div class="d-flex">
                    <button class="btn btn-dark btn-sm" @onclick="() => HandleRowUpdated(detail)">
                        Save
                    </button>

                    <!-- Cancel Button -->
                    <button class="btn btn-secondary btn-sm ms-2" @onclick="() => table.CancelEdit()">
                        Cancel
                    </button>
                    </div>
                </EditorTemplate>


            </Column>
        </ChildContent>
    </Table>
}
else
{
    <p>No enrollment details found.</p>
}
