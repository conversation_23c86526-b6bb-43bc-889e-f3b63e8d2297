﻿/* Sidebar Styles - Modern Light Theme */
.sidebar-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    height: 100vh;
    overflow-y: auto;
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
}

.sidebar-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    z-index: -1;
}

.sidebar-top-row {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sidebar-navbar-brand {
    color: #ffffff;
    font-size: 1.4rem;
    font-weight: 700;
    text-decoration: none;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.sidebar-navbar-brand:hover {
    color: #f0f9ff;
    transform: scale(1.02);
}

.sidebar-navbar-toggler {
    display: none;
}

.sidebar-nav-scrollable {
    padding: 1.5rem;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.sidebar-nav-item {
    margin-bottom: 0.25rem;
}

.sidebar-nav-link {
    color: #ffffff;
    text-decoration: none;
    padding: 1rem 1.25rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.sidebar-nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.sidebar-nav-link:hover::before {
    left: 100%;
}

.sidebar-nav-link:hover,
.sidebar-nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    transform: translateX(-5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.sidebar-nav-link button {
    background: none;
    border: none;
    color: inherit;
    font: inherit;
    width: 100%;
    text-align: inherit;
    padding: 0;
    cursor: pointer;
}

.sidebar-nav-icon {
    margin-left: 0.75rem;
    font-size: 1.3rem;
    opacity: 0.9;
    transition: all 0.3s ease;
}

.sidebar-nav-link:hover .sidebar-nav-icon {
    opacity: 1;
    transform: scale(1.1);
}

.sidebar-submenu {
    padding-right: 1.5rem;
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-submenu .sidebar-nav-link {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 0.25rem;
    border-radius: 8px;
}

.sidebar-submenu .sidebar-nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(-3px);
}

.sidebar-submenu .sidebar-nav-item {
    margin-bottom: 0;
}

/* Scrollbar Styling */
.sidebar-page::-webkit-scrollbar {
    width: 6px;
}

.sidebar-page::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-page::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar-page::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .sidebar-nav-scrollable {
        padding: 1rem;
    }

    .sidebar-nav-link {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
    }

    .sidebar-submenu .sidebar-nav-link {
        padding: 0.625rem 1.25rem;
        font-size: 0.9rem;
    }
}
