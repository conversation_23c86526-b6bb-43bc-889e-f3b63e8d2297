﻿.sidebar-page {
    background-color: #1f1f2e;
    color: #ffffff;
    height: 100vh;
    overflow-y: auto;
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar-top-row {
    background-color: #2d2d3a;
    padding: 1rem;
}

.sidebar-navbar-brand {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: bold;
    text-decoration: none;
}

.sidebar-navbar-toggler {
    display: none;
}

.sidebar-nav-scrollable {
    padding: 1rem;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
}

.sidebar-nav-item {
    margin-bottom: 0.5rem;
}

.sidebar-nav-link {
    color: #ffffff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease;
}

    .sidebar-nav-link:hover,
    .sidebar-nav-link.active {
        background-color: #3a3a4f;
    }

.sidebar-nav-icon {
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

.sidebar-submenu {
    padding-right: 1rem;
    margin-top: 0.5rem;
}

    .sidebar-submenu .sidebar-nav-link {
        padding: 0.5rem 1.5rem;
        font-size: 0.95rem;
    }
